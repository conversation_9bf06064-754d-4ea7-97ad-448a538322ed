// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.16.0
// source: GameService.proto

package natsrpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	public "liteframe/internal/common/protos/public"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid  string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *AuthReq) Reset() {
	*x = AuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthReq) ProtoMessage() {}

func (x *AuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthReq.ProtoReflect.Descriptor instead.
func (*AuthReq) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{0}
}

func (x *AuthReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *AuthReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type AuthResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *AuthResp) Reset() {
	*x = AuthResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthResp) ProtoMessage() {}

func (x *AuthResp) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthResp.ProtoReflect.Descriptor instead.
func (*AuthResp) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{1}
}

func (x *AuthResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AuthResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MatchResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success    bool                         `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                         // true if the match was successful
	Uid        uint64                       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`                                 // 请求匹配的玩家id
	BattleId   int64                        `protobuf:"varint,3,opt,name=battle_id,json=battleId,proto3" json:"battle_id,omitempty"`       // 房间id
	ServerId   int64                        `protobuf:"varint,5,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`       // 房间所在服务器id
	Target     []*public.PBBattlePlayerInfo `protobuf:"bytes,6,rep,name=target,proto3" json:"target,omitempty"`                            // 匹配到的玩家信息
	CreateTime int64                        `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"` // 房间创建时间
}

func (x *MatchResultRequest) Reset() {
	*x = MatchResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchResultRequest) ProtoMessage() {}

func (x *MatchResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchResultRequest.ProtoReflect.Descriptor instead.
func (*MatchResultRequest) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{2}
}

func (x *MatchResultRequest) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MatchResultRequest) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *MatchResultRequest) GetBattleId() int64 {
	if x != nil {
		return x.BattleId
	}
	return 0
}

func (x *MatchResultRequest) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *MatchResultRequest) GetTarget() []*public.PBBattlePlayerInfo {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *MatchResultRequest) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

type MatchResultResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 0表示成功，其他表示失败
}

func (x *MatchResultResponse) Reset() {
	*x = MatchResultResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchResultResponse) ProtoMessage() {}

func (x *MatchResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchResultResponse.ProtoReflect.Descriptor instead.
func (*MatchResultResponse) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{3}
}

func (x *MatchResultResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type BattleStateChangeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleId     int64              `protobuf:"varint,1,opt,name=battle_id,json=battleId,proto3" json:"battle_id,omitempty"`               // 战斗ID
	State        public.BattleState `protobuf:"varint,2,opt,name=state,proto3,enum=BattleState" json:"state,omitempty"`                    // 战斗状态
	RemainTimeMs int32              `protobuf:"varint,3,opt,name=remain_time_ms,json=remainTimeMs,proto3" json:"remain_time_ms,omitempty"` // 当前阶段剩余时间(毫秒)
	RoundCount   int32              `protobuf:"varint,4,opt,name=round_count,json=roundCount,proto3" json:"round_count,omitempty"`         // 当前回合数
}

func (x *BattleStateChangeReq) Reset() {
	*x = BattleStateChangeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleStateChangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleStateChangeReq) ProtoMessage() {}

func (x *BattleStateChangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleStateChangeReq.ProtoReflect.Descriptor instead.
func (*BattleStateChangeReq) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{4}
}

func (x *BattleStateChangeReq) GetBattleId() int64 {
	if x != nil {
		return x.BattleId
	}
	return 0
}

func (x *BattleStateChangeReq) GetState() public.BattleState {
	if x != nil {
		return x.State
	}
	return public.BattleState_STATE_NONE
}

func (x *BattleStateChangeReq) GetRemainTimeMs() int32 {
	if x != nil {
		return x.RemainTimeMs
	}
	return 0
}

func (x *BattleStateChangeReq) GetRoundCount() int32 {
	if x != nil {
		return x.RoundCount
	}
	return 0
}

type BattleStateChangeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 0表示成功，其他表示失败
}

func (x *BattleStateChangeResp) Reset() {
	*x = BattleStateChangeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleStateChangeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleStateChangeResp) ProtoMessage() {}

func (x *BattleStateChangeResp) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleStateChangeResp.ProtoReflect.Descriptor instead.
func (*BattleStateChangeResp) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{5}
}

func (x *BattleStateChangeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

//新回合开始
type RoundStartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                  uint64                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Buffers              []int32                 `protobuf:"varint,2,rep,packed,name=buffers,proto3" json:"buffers,omitempty"`                    // 3 个buffer选择
	PlayerBoards         []*public.PBPlayerBoard `protobuf:"bytes,3,rep,name=playerBoards,proto3" json:"playerBoards,omitempty"`                  // 双方玩家的棋盘信息
	TimeoutTimestamp     int64                   `protobuf:"varint,4,opt,name=timeoutTimestamp,proto3" json:"timeoutTimestamp,omitempty"`         // 回合战斗开始超时时间戳
	BuffTimeoutTimestamp int64                   `protobuf:"varint,5,opt,name=buffTimeoutTimestamp,proto3" json:"buffTimeoutTimestamp,omitempty"` // buff选择超时时间戳
	Skills               []int32                 `protobuf:"varint,6,rep,packed,name=skills,proto3" json:"skills,omitempty"`                      //携带的羁绊技能
}

func (x *RoundStartReq) Reset() {
	*x = RoundStartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoundStartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoundStartReq) ProtoMessage() {}

func (x *RoundStartReq) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoundStartReq.ProtoReflect.Descriptor instead.
func (*RoundStartReq) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{6}
}

func (x *RoundStartReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *RoundStartReq) GetBuffers() []int32 {
	if x != nil {
		return x.Buffers
	}
	return nil
}

func (x *RoundStartReq) GetPlayerBoards() []*public.PBPlayerBoard {
	if x != nil {
		return x.PlayerBoards
	}
	return nil
}

func (x *RoundStartReq) GetTimeoutTimestamp() int64 {
	if x != nil {
		return x.TimeoutTimestamp
	}
	return 0
}

func (x *RoundStartReq) GetBuffTimeoutTimestamp() int64 {
	if x != nil {
		return x.BuffTimeoutTimestamp
	}
	return 0
}

func (x *RoundStartReq) GetSkills() []int32 {
	if x != nil {
		return x.Skills
	}
	return nil
}

type RoundStartResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *RoundStartResp) Reset() {
	*x = RoundStartResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoundStartResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoundStartResp) ProtoMessage() {}

func (x *RoundStartResp) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoundStartResp.ProtoReflect.Descriptor instead.
func (*RoundStartResp) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{7}
}

func (x *RoundStartResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

//战斗开始
type RoundBattleStartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid              uint64                     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Seed             int32                      `protobuf:"varint,2,opt,name=seed,proto3" json:"seed,omitempty"` //随机种子
	BattleId         int64                      `protobuf:"varint,3,opt,name=battle_id,json=battleId,proto3" json:"battle_id,omitempty"`
	Team             []*public.PBBattleCampInfo `protobuf:"bytes,4,rep,name=team,proto3" json:"team,omitempty"`                          //对战双方的数据 第一个是先手方
	TimeoutTimestamp int64                      `protobuf:"varint,5,opt,name=timeoutTimestamp,proto3" json:"timeoutTimestamp,omitempty"` // 回合结算超时时间戳
}

func (x *RoundBattleStartReq) Reset() {
	*x = RoundBattleStartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoundBattleStartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoundBattleStartReq) ProtoMessage() {}

func (x *RoundBattleStartReq) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoundBattleStartReq.ProtoReflect.Descriptor instead.
func (*RoundBattleStartReq) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{8}
}

func (x *RoundBattleStartReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *RoundBattleStartReq) GetSeed() int32 {
	if x != nil {
		return x.Seed
	}
	return 0
}

func (x *RoundBattleStartReq) GetBattleId() int64 {
	if x != nil {
		return x.BattleId
	}
	return 0
}

func (x *RoundBattleStartReq) GetTeam() []*public.PBBattleCampInfo {
	if x != nil {
		return x.Team
	}
	return nil
}

func (x *RoundBattleStartReq) GetTimeoutTimestamp() int64 {
	if x != nil {
		return x.TimeoutTimestamp
	}
	return 0
}

type RoundBattleStartResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *RoundBattleStartResp) Reset() {
	*x = RoundBattleStartResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoundBattleStartResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoundBattleStartResp) ProtoMessage() {}

func (x *RoundBattleStartResp) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoundBattleStartResp.ProtoReflect.Descriptor instead.
func (*RoundBattleStartResp) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{9}
}

func (x *RoundBattleStartResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type RoundBattleEndReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid              uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WinUid           uint64 `protobuf:"varint,2,opt,name=winUid,proto3" json:"winUid,omitempty"`
	LoseUid          uint64 `protobuf:"varint,3,opt,name=loseUid,proto3" json:"loseUid,omitempty"`
	IsEnd            bool   `protobuf:"varint,4,opt,name=isEnd,proto3" json:"isEnd,omitempty"`
	TimeoutTimestamp int64  `protobuf:"varint,5,opt,name=timeoutTimestamp,proto3" json:"timeoutTimestamp,omitempty"` // 下一轮新回合开始超时时间戳
}

func (x *RoundBattleEndReq) Reset() {
	*x = RoundBattleEndReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoundBattleEndReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoundBattleEndReq) ProtoMessage() {}

func (x *RoundBattleEndReq) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoundBattleEndReq.ProtoReflect.Descriptor instead.
func (*RoundBattleEndReq) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{10}
}

func (x *RoundBattleEndReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *RoundBattleEndReq) GetWinUid() uint64 {
	if x != nil {
		return x.WinUid
	}
	return 0
}

func (x *RoundBattleEndReq) GetLoseUid() uint64 {
	if x != nil {
		return x.LoseUid
	}
	return 0
}

func (x *RoundBattleEndReq) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *RoundBattleEndReq) GetTimeoutTimestamp() int64 {
	if x != nil {
		return x.TimeoutTimestamp
	}
	return 0
}

type RoundBattleEndResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *RoundBattleEndResp) Reset() {
	*x = RoundBattleEndResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoundBattleEndResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoundBattleEndResp) ProtoMessage() {}

func (x *RoundBattleEndResp) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoundBattleEndResp.ProtoReflect.Descriptor instead.
func (*RoundBattleEndResp) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{11}
}

func (x *RoundBattleEndResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

//整场战斗结束
type BattleEndReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid       uint64                     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                              // 玩家ID
	BattleId  int64                      `protobuf:"varint,2,opt,name=battle_id,json=battleId,proto3" json:"battle_id,omitempty"`    // 战斗ID
	Rank      int32                      `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`                            // 最终排名
	WinStreak int32                      `protobuf:"varint,4,opt,name=win_streak,json=winStreak,proto3" json:"win_streak,omitempty"` // 战斗结束时的连胜场次
	Heros     []*public.PBBattleHeroInfo `protobuf:"bytes,5,rep,name=heros,proto3" json:"heros,omitempty"`                           // 最终阵容
}

func (x *BattleEndReq) Reset() {
	*x = BattleEndReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleEndReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleEndReq) ProtoMessage() {}

func (x *BattleEndReq) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleEndReq.ProtoReflect.Descriptor instead.
func (*BattleEndReq) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{12}
}

func (x *BattleEndReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *BattleEndReq) GetBattleId() int64 {
	if x != nil {
		return x.BattleId
	}
	return 0
}

func (x *BattleEndReq) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *BattleEndReq) GetWinStreak() int32 {
	if x != nil {
		return x.WinStreak
	}
	return 0
}

func (x *BattleEndReq) GetHeros() []*public.PBBattleHeroInfo {
	if x != nil {
		return x.Heros
	}
	return nil
}

//整场战斗结束
type BattleEndResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *BattleEndResp) Reset() {
	*x = BattleEndResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameService_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleEndResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleEndResp) ProtoMessage() {}

func (x *BattleEndResp) ProtoReflect() protoreflect.Message {
	mi := &file_GameService_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleEndResp.ProtoReflect.Descriptor instead.
func (*BattleEndResp) Descriptor() ([]byte, []int) {
	return file_GameService_proto_rawDescGZIP(), []int{13}
}

func (x *BattleEndResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_GameService_proto protoreflect.FileDescriptor

var file_GameService_proto_rawDesc = []byte{
	0x0a, 0x11, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x1a, 0x10, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x07, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x38, 0x0a, 0x08, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0xc8, 0x01, 0x0a, 0x12, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x2b, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x29, 0x0a, 0x13, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x14, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0c, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65,
	0x6d, 0x61, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2b, 0x0a, 0x15, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xe7, 0x01, 0x0a, 0x0d, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x62,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x32, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x50,
	0x42, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x0c, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x32, 0x0a, 0x14, 0x62, 0x75, 0x66, 0x66, 0x54, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x62, 0x75, 0x66, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6b,
	0x69, 0x6c, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x73, 0x6b, 0x69, 0x6c,
	0x6c, 0x73, 0x22, 0x24, 0x0a, 0x0e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xab, 0x01, 0x0a, 0x13, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x65, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x43, 0x61, 0x6d, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x2a, 0x0a, 0x14, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x11, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x45, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x69,
	0x6e, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x77, 0x69, 0x6e, 0x55,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x73, 0x65, 0x55, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x6f, 0x73, 0x65, 0x55, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x73, 0x45, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x45,
	0x6e, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x28,
	0x0a, 0x12, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x0c, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a,
	0x77, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x77, 0x69, 0x6e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x12, 0x27, 0x0a, 0x05, 0x68,
	0x65, 0x72, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x68,
	0x65, 0x72, 0x6f, 0x73, 0x22, 0x23, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x32, 0xb4, 0x04, 0x0a, 0x0b, 0x47, 0x61,
	0x6d, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x41, 0x75, 0x74,
	0x68, 0x12, 0x10, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x12, 0x52, 0x0a, 0x0b,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x2e, 0x6e, 0x61,
	0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72,
	0x70, 0x63, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x08, 0x80, 0xb5, 0x18, 0x01, 0x90, 0xb5, 0x18, 0x01,
	0x12, 0x47, 0x0a, 0x0a, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x16,
	0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63,
	0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x08, 0x80, 0xb5, 0x18, 0x01, 0x90, 0xb5, 0x18, 0x01, 0x12, 0x59, 0x0a, 0x10, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x2e,
	0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x6e, 0x61,
	0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x08, 0x80, 0xb5, 0x18, 0x01,
	0x90, 0xb5, 0x18, 0x01, 0x12, 0x53, 0x0a, 0x0e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x1a, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63,
	0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x08, 0x80, 0xb5, 0x18, 0x01, 0x90, 0xb5, 0x18, 0x01, 0x12, 0x44, 0x0a, 0x09, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x15, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63,
	0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x08, 0x80, 0xb5, 0x18, 0x01, 0x90, 0xb5, 0x18, 0x01, 0x12,
	0x5f, 0x0a, 0x14, 0x4f, 0x6e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x12, 0x1d, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70,
	0x63, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63,
	0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x08, 0x80, 0xb5, 0x18, 0x01, 0x90, 0xb5, 0x18, 0x01,
	0x42, 0x3a, 0x5a, 0x21, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61,
	0x74, 0x73, 0x72, 0x70, 0x63, 0xaa, 0x02, 0x14, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_GameService_proto_rawDescOnce sync.Once
	file_GameService_proto_rawDescData = file_GameService_proto_rawDesc
)

func file_GameService_proto_rawDescGZIP() []byte {
	file_GameService_proto_rawDescOnce.Do(func() {
		file_GameService_proto_rawDescData = protoimpl.X.CompressGZIP(file_GameService_proto_rawDescData)
	})
	return file_GameService_proto_rawDescData
}

var file_GameService_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_GameService_proto_goTypes = []interface{}{
	(*AuthReq)(nil),                   // 0: natsrpc.AuthReq
	(*AuthResp)(nil),                  // 1: natsrpc.AuthResp
	(*MatchResultRequest)(nil),        // 2: natsrpc.MatchResultRequest
	(*MatchResultResponse)(nil),       // 3: natsrpc.MatchResultResponse
	(*BattleStateChangeReq)(nil),      // 4: natsrpc.BattleStateChangeReq
	(*BattleStateChangeResp)(nil),     // 5: natsrpc.BattleStateChangeResp
	(*RoundStartReq)(nil),             // 6: natsrpc.RoundStartReq
	(*RoundStartResp)(nil),            // 7: natsrpc.RoundStartResp
	(*RoundBattleStartReq)(nil),       // 8: natsrpc.RoundBattleStartReq
	(*RoundBattleStartResp)(nil),      // 9: natsrpc.RoundBattleStartResp
	(*RoundBattleEndReq)(nil),         // 10: natsrpc.RoundBattleEndReq
	(*RoundBattleEndResp)(nil),        // 11: natsrpc.RoundBattleEndResp
	(*BattleEndReq)(nil),              // 12: natsrpc.BattleEndReq
	(*BattleEndResp)(nil),             // 13: natsrpc.BattleEndResp
	(*public.PBBattlePlayerInfo)(nil), // 14: PBBattlePlayerInfo
	(public.BattleState)(0),           // 15: BattleState
	(*public.PBPlayerBoard)(nil),      // 16: PBPlayerBoard
	(*public.PBBattleCampInfo)(nil),   // 17: PBBattleCampInfo
	(*public.PBBattleHeroInfo)(nil),   // 18: PBBattleHeroInfo
}
var file_GameService_proto_depIdxs = []int32{
	14, // 0: natsrpc.MatchResultRequest.target:type_name -> PBBattlePlayerInfo
	15, // 1: natsrpc.BattleStateChangeReq.state:type_name -> BattleState
	16, // 2: natsrpc.RoundStartReq.playerBoards:type_name -> PBPlayerBoard
	17, // 3: natsrpc.RoundBattleStartReq.team:type_name -> PBBattleCampInfo
	18, // 4: natsrpc.BattleEndReq.heros:type_name -> PBBattleHeroInfo
	0,  // 5: natsrpc.GameService.Auth:input_type -> natsrpc.AuthReq
	2,  // 6: natsrpc.GameService.MatchResult:input_type -> natsrpc.MatchResultRequest
	6,  // 7: natsrpc.GameService.RoundStart:input_type -> natsrpc.RoundStartReq
	8,  // 8: natsrpc.GameService.RoundBattleStart:input_type -> natsrpc.RoundBattleStartReq
	10, // 9: natsrpc.GameService.RoundBattleEnd:input_type -> natsrpc.RoundBattleEndReq
	12, // 10: natsrpc.GameService.BattleEnd:input_type -> natsrpc.BattleEndReq
	4,  // 11: natsrpc.GameService.OnBattleStateChanged:input_type -> natsrpc.BattleStateChangeReq
	1,  // 12: natsrpc.GameService.Auth:output_type -> natsrpc.AuthResp
	3,  // 13: natsrpc.GameService.MatchResult:output_type -> natsrpc.MatchResultResponse
	7,  // 14: natsrpc.GameService.RoundStart:output_type -> natsrpc.RoundStartResp
	9,  // 15: natsrpc.GameService.RoundBattleStart:output_type -> natsrpc.RoundBattleStartResp
	11, // 16: natsrpc.GameService.RoundBattleEnd:output_type -> natsrpc.RoundBattleEndResp
	13, // 17: natsrpc.GameService.BattleEnd:output_type -> natsrpc.BattleEndResp
	5,  // 18: natsrpc.GameService.OnBattleStateChanged:output_type -> natsrpc.BattleStateChangeResp
	12, // [12:19] is the sub-list for method output_type
	5,  // [5:12] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_GameService_proto_init() }
func file_GameService_proto_init() {
	if File_GameService_proto != nil {
		return
	}
	file_descriptor_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_GameService_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchResultResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattleStateChangeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattleStateChangeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoundStartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoundStartResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoundBattleStartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoundBattleStartResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoundBattleEndReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoundBattleEndResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattleEndReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameService_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattleEndResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_GameService_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_GameService_proto_goTypes,
		DependencyIndexes: file_GameService_proto_depIdxs,
		MessageInfos:      file_GameService_proto_msgTypes,
	}.Build()
	File_GameService_proto = out.File
	file_GameService_proto_rawDesc = nil
	file_GameService_proto_goTypes = nil
	file_GameService_proto_depIdxs = nil
}
