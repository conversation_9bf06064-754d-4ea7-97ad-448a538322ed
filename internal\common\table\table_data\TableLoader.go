// Code generated by tb_gen. DO NOT EDIT.

package table_data

import (
	"fmt"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"time"

	"liteframe/pkg/log"
)

type ITable interface {
	load(dir string, reader tablereader.ITableReader) error
	reload(dir string) (bool, error)
	GetFileName() string
	MD5() string
}

// TableLoader
type TableLoader struct {
	dir  string
	list []ITable

	TableActivity    *TableActivityData
	TableActivityBossDamage    *TableActivityBossDamageData
	TableActivityCard    *TableActivityCardData
	TableActivityChallenge    *TableActivityChallengeData
	TableActivityDoorGift    *TableActivityDoorGiftData
	TableActivityExchangeShop    *TableActivityExchangeShopData
	TableActivityFund    *TableActivityFundData
	TableActivityGame    *TableActivityGameData
	TableActivityGift    *TableActivityGiftData
	TableActivityHeroRoad    *TableActivityHeroRoadData
	TableActivityLimitGift    *TableActivityLimitGiftData
	TableActivityLotteryRewards    *TableActivityLotteryRewardsData
	TableActivityMiracleDrop    *TableActivityMiracleDropData
	TableActivitySevenDayTask    *TableActivitySevenDayTaskData
	TableActivityTask    *TableActivityTaskData
	TableActivityTimeConfig    *TableActivityTimeConfigData
	TableActivityTurntable    *TableActivityTurntableData
	TableAttr    *TableAttrData
	TableAttrLevel    *TableAttrLevelData
	TableAttrRange    *TableAttrRangeData
	TableBuff    *TableBuffData
	TableChessHero    *TableChessHeroData
	TableCommonBoxReward    *TableCommonBoxRewardData
	TableDoorGift    *TableDoorGiftData
	TableDropBox    *TableDropBoxData
	TableDropGroup    *TableDropGroupData
	TableEnergyBuy    *TableEnergyBuyData
	TableFCMTemplate    *TableFCMTemplateData
	TableFashionableDress    *TableFashionableDressData
	TableFirstCharge    *TableFirstChargeData
	TableFollowGift    *TableFollowGiftData
	TableFunctionPreview    *TableFunctionPreviewData
	TableFunctionUnlock    *TableFunctionUnlockData
	TableGachaBonus    *TableGachaBonusData
	TableGameConfig    *TableGameConfigData
	TableGiftCode    *TableGiftCodeData
	TableGradedFund    *TableGradedFundData
	TableGradedFundInfo    *TableGradedFundInfoData
	TableGuideLinesInfo    *TableGuideLinesInfoData
	TableGuideLinesTrigger    *TableGuideLinesTriggerData
	TableGuildActivityBoss    *TableGuildActivityBossData
	TableGuildConfig    *TableGuildConfigData
	TableGuildDonate    *TableGuildDonateData
	TableGuildLevel    *TableGuildLevelData
	TableGuildShop    *TableGuildShopData
	TableGuildShopList    *TableGuildShopListData
	TableGuildTech    *TableGuildTechData
	TableHeadFrame    *TableHeadFrameData
	TableHeadIcon    *TableHeadIconData
	TableHeavenlyDao    *TableHeavenlyDaoData
	TableHero    *TableHeroData
	TableHeroEvo    *TableHeroEvoData
	TableHeroLevel    *TableHeroLevelData
	TableHook    *TableHookData
	TableInviteTask    *TableInviteTaskData
	TableIronSourceADConfig    *TableIronSourceADConfigData
	TableItem    *TableItemData
	TableLanguage    *TableLanguageData
	TableMail    *TableMailData
	TableMailTemplateConfig    *TableMailTemplateConfigData
	TableMainLine    *TableMainLineData
	TableMainRank    *TableMainRankData
	TableMainRankSeason    *TableMainRankSeasonData
	TableMidasDiamond    *TableMidasDiamondData
	TableMidasItem    *TableMidasItemData
	TableMission    *TableMissionData
	TableModel    *TableModelData
	TableMonsterBaseAttr    *TableMonsterBaseAttrData
	TableMonthlyCard    *TableMonthlyCardData
	TableMonthlyCardNew    *TableMonthlyCardNewData
	TableMonthlyCardNewExtraReward    *TableMonthlyCardNewExtraRewardData
	TableNewGuideLinesInfo    *TableNewGuideLinesInfoData
	TableNewGuideLinesTrigger    *TableNewGuideLinesTriggerData
	TablePayment    *TablePaymentData
	TablePlayMode    *TablePlayModeData
	TablePlayerBaseAttr    *TablePlayerBaseAttrData
	TablePlayerLevel    *TablePlayerLevelData
	TableQuestionnaire    *TableQuestionnaireData
	TableRandomName    *TableRandomNameData
	TableRank    *TableRankData
	TableRankBoard    *TableRankBoardData
	TableSchedulerConfig    *TableSchedulerConfigData
	TableServerSkill    *TableServerSkillData
	TableSevenSignIn    *TableSevenSignInData
	TableShopGiftPack    *TableShopGiftPackData
	TableSignBonus    *TableSignBonusData
	TableSignIn    *TableSignInData
	TableSynergy    *TableSynergyData
	TableTeamHole    *TableTeamHoleData
	TableTimeGiftPacks    *TableTimeGiftPacksData
	TableTopupRebate    *TableTopupRebateData
	TableTotalRecharge    *TableTotalRechargeData
	TableTowerReward    *TableTowerRewardData
	TableTreasure    *TableTreasureData
	TableTreasureGacha    *TableTreasureGachaData
	TableTreasureGachaPro    *TableTreasureGachaProData
	TableTreasureLv    *TableTreasureLvData
	TableTreasureStar    *TableTreasureStarData
	TableaccruedRewards    *TableaccruedRewardsData
}

// NewTableLoader
func NewTableLoader(dir string) *TableLoader {
	t := &TableLoader{
		dir: dir,

		TableActivity: &TableActivityData{file: "Activity.json"},
		TableActivityBossDamage: &TableActivityBossDamageData{file: "ActivityBossDamage.json"},
		TableActivityCard: &TableActivityCardData{file: "ActivityCard.json"},
		TableActivityChallenge: &TableActivityChallengeData{file: "ActivityChallenge.json"},
		TableActivityDoorGift: &TableActivityDoorGiftData{file: "ActivityDoorGift.json"},
		TableActivityExchangeShop: &TableActivityExchangeShopData{file: "ActivityExchangeShop.json"},
		TableActivityFund: &TableActivityFundData{file: "ActivityFund.json"},
		TableActivityGame: &TableActivityGameData{file: "ActivityGame.json"},
		TableActivityGift: &TableActivityGiftData{file: "ActivityGift.json"},
		TableActivityHeroRoad: &TableActivityHeroRoadData{file: "ActivityHeroRoad.json"},
		TableActivityLimitGift: &TableActivityLimitGiftData{file: "ActivityLimitGift.json"},
		TableActivityLotteryRewards: &TableActivityLotteryRewardsData{file: "ActivityLotteryRewards.json"},
		TableActivityMiracleDrop: &TableActivityMiracleDropData{file: "ActivityMiracleDrop.json"},
		TableActivitySevenDayTask: &TableActivitySevenDayTaskData{file: "ActivitySevenDayTask.json"},
		TableActivityTask: &TableActivityTaskData{file: "ActivityTask.json"},
		TableActivityTimeConfig: &TableActivityTimeConfigData{file: "ActivityTimeConfig.json"},
		TableActivityTurntable: &TableActivityTurntableData{file: "ActivityTurntable.json"},
		TableAttr: &TableAttrData{file: "Attr.json"},
		TableAttrLevel: &TableAttrLevelData{file: "AttrLevel.json"},
		TableAttrRange: &TableAttrRangeData{file: "AttrRange.json"},
		TableBuff: &TableBuffData{file: "Buff.json"},
		TableChessHero: &TableChessHeroData{file: "ChessHero.json"},
		TableCommonBoxReward: &TableCommonBoxRewardData{file: "CommonBoxReward.json"},
		TableDoorGift: &TableDoorGiftData{file: "DoorGift.json"},
		TableDropBox: &TableDropBoxData{file: "DropBox.json"},
		TableDropGroup: &TableDropGroupData{file: "DropGroup.json"},
		TableEnergyBuy: &TableEnergyBuyData{file: "EnergyBuy.json"},
		TableFCMTemplate: &TableFCMTemplateData{file: "FCMTemplate.json"},
		TableFashionableDress: &TableFashionableDressData{file: "FashionableDress.json"},
		TableFirstCharge: &TableFirstChargeData{file: "FirstCharge.json"},
		TableFollowGift: &TableFollowGiftData{file: "FollowGift.json"},
		TableFunctionPreview: &TableFunctionPreviewData{file: "FunctionPreview.json"},
		TableFunctionUnlock: &TableFunctionUnlockData{file: "FunctionUnlock.json"},
		TableGachaBonus: &TableGachaBonusData{file: "GachaBonus.json"},
		TableGameConfig: &TableGameConfigData{file: "GameConfig.json"},
		TableGiftCode: &TableGiftCodeData{file: "GiftCode.json"},
		TableGradedFund: &TableGradedFundData{file: "GradedFund.json"},
		TableGradedFundInfo: &TableGradedFundInfoData{file: "GradedFundInfo.json"},
		TableGuideLinesInfo: &TableGuideLinesInfoData{file: "GuideLinesInfo.json"},
		TableGuideLinesTrigger: &TableGuideLinesTriggerData{file: "GuideLinesTrigger.json"},
		TableGuildActivityBoss: &TableGuildActivityBossData{file: "GuildActivityBoss.json"},
		TableGuildConfig: &TableGuildConfigData{file: "GuildConfig.json"},
		TableGuildDonate: &TableGuildDonateData{file: "GuildDonate.json"},
		TableGuildLevel: &TableGuildLevelData{file: "GuildLevel.json"},
		TableGuildShop: &TableGuildShopData{file: "GuildShop.json"},
		TableGuildShopList: &TableGuildShopListData{file: "GuildShopList.json"},
		TableGuildTech: &TableGuildTechData{file: "GuildTech.json"},
		TableHeadFrame: &TableHeadFrameData{file: "HeadFrame.json"},
		TableHeadIcon: &TableHeadIconData{file: "HeadIcon.json"},
		TableHeavenlyDao: &TableHeavenlyDaoData{file: "HeavenlyDao.json"},
		TableHero: &TableHeroData{file: "Hero.json"},
		TableHeroEvo: &TableHeroEvoData{file: "HeroEvo.json"},
		TableHeroLevel: &TableHeroLevelData{file: "HeroLevel.json"},
		TableHook: &TableHookData{file: "Hook.json"},
		TableInviteTask: &TableInviteTaskData{file: "InviteTask.json"},
		TableIronSourceADConfig: &TableIronSourceADConfigData{file: "IronSourceADConfig.json"},
		TableItem: &TableItemData{file: "Item.json"},
		TableLanguage: &TableLanguageData{file: "Language.json"},
		TableMail: &TableMailData{file: "Mail.json"},
		TableMailTemplateConfig: &TableMailTemplateConfigData{file: "MailTemplateConfig.json"},
		TableMainLine: &TableMainLineData{file: "MainLine.json"},
		TableMainRank: &TableMainRankData{file: "MainRank.json"},
		TableMainRankSeason: &TableMainRankSeasonData{file: "MainRankSeason.json"},
		TableMidasDiamond: &TableMidasDiamondData{file: "MidasDiamond.json"},
		TableMidasItem: &TableMidasItemData{file: "MidasItem.json"},
		TableMission: &TableMissionData{file: "Mission.json"},
		TableModel: &TableModelData{file: "Model.json"},
		TableMonsterBaseAttr: &TableMonsterBaseAttrData{file: "MonsterBaseAttr.json"},
		TableMonthlyCard: &TableMonthlyCardData{file: "MonthlyCard.json"},
		TableMonthlyCardNew: &TableMonthlyCardNewData{file: "MonthlyCardNew.json"},
		TableMonthlyCardNewExtraReward: &TableMonthlyCardNewExtraRewardData{file: "MonthlyCardNewExtraReward.json"},
		TableNewGuideLinesInfo: &TableNewGuideLinesInfoData{file: "NewGuideLinesInfo.json"},
		TableNewGuideLinesTrigger: &TableNewGuideLinesTriggerData{file: "NewGuideLinesTrigger.json"},
		TablePayment: &TablePaymentData{file: "Payment.json"},
		TablePlayMode: &TablePlayModeData{file: "PlayMode.json"},
		TablePlayerBaseAttr: &TablePlayerBaseAttrData{file: "PlayerBaseAttr.json"},
		TablePlayerLevel: &TablePlayerLevelData{file: "PlayerLevel.json"},
		TableQuestionnaire: &TableQuestionnaireData{file: "Questionnaire.json"},
		TableRandomName: &TableRandomNameData{file: "RandomName.json"},
		TableRank: &TableRankData{file: "Rank.json"},
		TableRankBoard: &TableRankBoardData{file: "RankBoard.json"},
		TableSchedulerConfig: &TableSchedulerConfigData{file: "SchedulerConfig.json"},
		TableServerSkill: &TableServerSkillData{file: "ServerSkill.json"},
		TableSevenSignIn: &TableSevenSignInData{file: "SevenSignIn.json"},
		TableShopGiftPack: &TableShopGiftPackData{file: "ShopGiftPack.json"},
		TableSignBonus: &TableSignBonusData{file: "SignBonus.json"},
		TableSignIn: &TableSignInData{file: "SignIn.json"},
		TableSynergy: &TableSynergyData{file: "Synergy.json"},
		TableTeamHole: &TableTeamHoleData{file: "TeamHole.json"},
		TableTimeGiftPacks: &TableTimeGiftPacksData{file: "TimeGiftPacks.json"},
		TableTopupRebate: &TableTopupRebateData{file: "TopupRebate.json"},
		TableTotalRecharge: &TableTotalRechargeData{file: "TotalRecharge.json"},
		TableTowerReward: &TableTowerRewardData{file: "TowerReward.json"},
		TableTreasure: &TableTreasureData{file: "Treasure.json"},
		TableTreasureGacha: &TableTreasureGachaData{file: "TreasureGacha.json"},
		TableTreasureGachaPro: &TableTreasureGachaProData{file: "TreasureGachaPro.json"},
		TableTreasureLv: &TableTreasureLvData{file: "TreasureLv.json"},
		TableTreasureStar: &TableTreasureStarData{file: "TreasureStar.json"},
		TableaccruedRewards: &TableaccruedRewardsData{file: "accruedRewards.json"},
	}

	t.list = []ITable{
		t.TableActivity,
		t.TableActivityBossDamage,
		t.TableActivityCard,
		t.TableActivityChallenge,
		t.TableActivityDoorGift,
		t.TableActivityExchangeShop,
		t.TableActivityFund,
		t.TableActivityGame,
		t.TableActivityGift,
		t.TableActivityHeroRoad,
		t.TableActivityLimitGift,
		t.TableActivityLotteryRewards,
		t.TableActivityMiracleDrop,
		t.TableActivitySevenDayTask,
		t.TableActivityTask,
		t.TableActivityTimeConfig,
		t.TableActivityTurntable,
		t.TableAttr,
		t.TableAttrLevel,
		t.TableAttrRange,
		t.TableBuff,
		t.TableChessHero,
		t.TableCommonBoxReward,
		t.TableDoorGift,
		t.TableDropBox,
		t.TableDropGroup,
		t.TableEnergyBuy,
		t.TableFCMTemplate,
		t.TableFashionableDress,
		t.TableFirstCharge,
		t.TableFollowGift,
		t.TableFunctionPreview,
		t.TableFunctionUnlock,
		t.TableGachaBonus,
		t.TableGameConfig,
		t.TableGiftCode,
		t.TableGradedFund,
		t.TableGradedFundInfo,
		t.TableGuideLinesInfo,
		t.TableGuideLinesTrigger,
		t.TableGuildActivityBoss,
		t.TableGuildConfig,
		t.TableGuildDonate,
		t.TableGuildLevel,
		t.TableGuildShop,
		t.TableGuildShopList,
		t.TableGuildTech,
		t.TableHeadFrame,
		t.TableHeadIcon,
		t.TableHeavenlyDao,
		t.TableHero,
		t.TableHeroEvo,
		t.TableHeroLevel,
		t.TableHook,
		t.TableInviteTask,
		t.TableIronSourceADConfig,
		t.TableItem,
		t.TableLanguage,
		t.TableMail,
		t.TableMailTemplateConfig,
		t.TableMainLine,
		t.TableMainRank,
		t.TableMainRankSeason,
		t.TableMidasDiamond,
		t.TableMidasItem,
		t.TableMission,
		t.TableModel,
		t.TableMonsterBaseAttr,
		t.TableMonthlyCard,
		t.TableMonthlyCardNew,
		t.TableMonthlyCardNewExtraReward,
		t.TableNewGuideLinesInfo,
		t.TableNewGuideLinesTrigger,
		t.TablePayment,
		t.TablePlayMode,
		t.TablePlayerBaseAttr,
		t.TablePlayerLevel,
		t.TableQuestionnaire,
		t.TableRandomName,
		t.TableRank,
		t.TableRankBoard,
		t.TableSchedulerConfig,
		t.TableServerSkill,
		t.TableSevenSignIn,
		t.TableShopGiftPack,
		t.TableSignBonus,
		t.TableSignIn,
		t.TableSynergy,
		t.TableTeamHole,
		t.TableTimeGiftPacks,
		t.TableTopupRebate,
		t.TableTotalRecharge,
		t.TableTowerReward,
		t.TableTreasure,
		t.TableTreasureGacha,
		t.TableTreasureGachaPro,
		t.TableTreasureLv,
		t.TableTreasureStar,
		t.TableaccruedRewards,
	}
	return t
}

// Load 加载所有表格
func (t *TableLoader) Load(reader tablereader.ITableReader) error {
	for _, v := range t.list {
		if e := v.load(t.dir, reader); nil != e {
			return fmt.Errorf(v.GetFileName() + ":" + e.Error())
		}
	}
	return nil
}

// Reload 重新加载所有表格
// 1、Reload的表不会减少条数，比如A表原来有100条，然后给改成99条，Reload完还是100条
// 2、Reload不会改变数组长度，只能改变值，[1,2,3]然后表改成[2,2]，Reload后实际是[2,2,3]
func (t *TableLoader) Reload() (ret []string, err error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if panicErr := recover(); nil != panicErr {
			err = fmt.Errorf("%v", panicErr)
			stack := debug.Stack()
			fmt.Println("[Table.Reload]", err, stack)
			log.Fatal("[Table.Reload] %v %s", log.Err(err), log.Kv("stack", stack))
		}
	}()

	begin := time.Now()
	for _, v := range t.list {
		ok, e := v.reload(t.dir)
		if ok {
			ret = append(ret, v.GetFileName())
		} else if e != nil {
			log.Error("reload failed", log.Kv("file_name", v.GetFileName()), log.Err(e))
			err = e
		}
	}

	cost := time.Since(begin)
	log.Info("reload table", log.Kv("cost_ms", cost.Milliseconds()))

	return
}

// GetTableMD5 获取md5
func (t *TableLoader) GetTableMD5(name string) string {
	for _, v := range t.list {
		if name == v.GetFileName() {
			return v.MD5()
		}
	}
	return ""
}

// Clone
func (t *TableLoader) Clone() *TableLoader {
	nt := &TableLoader{
		dir: t.dir,


		TableActivity: t.TableActivity.Clone().(*TableActivityData),

		TableActivityBossDamage: t.TableActivityBossDamage.Clone().(*TableActivityBossDamageData),

		TableActivityCard: t.TableActivityCard.Clone().(*TableActivityCardData),

		TableActivityChallenge: t.TableActivityChallenge.Clone().(*TableActivityChallengeData),

		TableActivityDoorGift: t.TableActivityDoorGift.Clone().(*TableActivityDoorGiftData),

		TableActivityExchangeShop: t.TableActivityExchangeShop.Clone().(*TableActivityExchangeShopData),

		TableActivityFund: t.TableActivityFund.Clone().(*TableActivityFundData),

		TableActivityGame: t.TableActivityGame.Clone().(*TableActivityGameData),

		TableActivityGift: t.TableActivityGift.Clone().(*TableActivityGiftData),

		TableActivityHeroRoad: t.TableActivityHeroRoad.Clone().(*TableActivityHeroRoadData),

		TableActivityLimitGift: t.TableActivityLimitGift.Clone().(*TableActivityLimitGiftData),

		TableActivityLotteryRewards: t.TableActivityLotteryRewards.Clone().(*TableActivityLotteryRewardsData),

		TableActivityMiracleDrop: t.TableActivityMiracleDrop.Clone().(*TableActivityMiracleDropData),

		TableActivitySevenDayTask: t.TableActivitySevenDayTask.Clone().(*TableActivitySevenDayTaskData),

		TableActivityTask: t.TableActivityTask.Clone().(*TableActivityTaskData),

		TableActivityTimeConfig: t.TableActivityTimeConfig.Clone().(*TableActivityTimeConfigData),

		TableActivityTurntable: t.TableActivityTurntable.Clone().(*TableActivityTurntableData),

		TableAttr: t.TableAttr.Clone().(*TableAttrData),

		TableAttrLevel: t.TableAttrLevel.Clone().(*TableAttrLevelData),

		TableAttrRange: t.TableAttrRange.Clone().(*TableAttrRangeData),

		TableBuff: t.TableBuff.Clone().(*TableBuffData),

		TableChessHero: t.TableChessHero.Clone().(*TableChessHeroData),

		TableCommonBoxReward: t.TableCommonBoxReward.Clone().(*TableCommonBoxRewardData),

		TableDoorGift: t.TableDoorGift.Clone().(*TableDoorGiftData),

		TableDropBox: t.TableDropBox.Clone().(*TableDropBoxData),

		TableDropGroup: t.TableDropGroup.Clone().(*TableDropGroupData),

		TableEnergyBuy: t.TableEnergyBuy.Clone().(*TableEnergyBuyData),

		TableFCMTemplate: t.TableFCMTemplate.Clone().(*TableFCMTemplateData),

		TableFashionableDress: t.TableFashionableDress.Clone().(*TableFashionableDressData),

		TableFirstCharge: t.TableFirstCharge.Clone().(*TableFirstChargeData),

		TableFollowGift: t.TableFollowGift.Clone().(*TableFollowGiftData),

		TableFunctionPreview: t.TableFunctionPreview.Clone().(*TableFunctionPreviewData),

		TableFunctionUnlock: t.TableFunctionUnlock.Clone().(*TableFunctionUnlockData),

		TableGachaBonus: t.TableGachaBonus.Clone().(*TableGachaBonusData),

		TableGameConfig: t.TableGameConfig.Clone().(*TableGameConfigData),

		TableGiftCode: t.TableGiftCode.Clone().(*TableGiftCodeData),

		TableGradedFund: t.TableGradedFund.Clone().(*TableGradedFundData),

		TableGradedFundInfo: t.TableGradedFundInfo.Clone().(*TableGradedFundInfoData),

		TableGuideLinesInfo: t.TableGuideLinesInfo.Clone().(*TableGuideLinesInfoData),

		TableGuideLinesTrigger: t.TableGuideLinesTrigger.Clone().(*TableGuideLinesTriggerData),

		TableGuildActivityBoss: t.TableGuildActivityBoss.Clone().(*TableGuildActivityBossData),

		TableGuildConfig: t.TableGuildConfig.Clone().(*TableGuildConfigData),

		TableGuildDonate: t.TableGuildDonate.Clone().(*TableGuildDonateData),

		TableGuildLevel: t.TableGuildLevel.Clone().(*TableGuildLevelData),

		TableGuildShop: t.TableGuildShop.Clone().(*TableGuildShopData),

		TableGuildShopList: t.TableGuildShopList.Clone().(*TableGuildShopListData),

		TableGuildTech: t.TableGuildTech.Clone().(*TableGuildTechData),

		TableHeadFrame: t.TableHeadFrame.Clone().(*TableHeadFrameData),

		TableHeadIcon: t.TableHeadIcon.Clone().(*TableHeadIconData),

		TableHeavenlyDao: t.TableHeavenlyDao.Clone().(*TableHeavenlyDaoData),

		TableHero: t.TableHero.Clone().(*TableHeroData),

		TableHeroEvo: t.TableHeroEvo.Clone().(*TableHeroEvoData),

		TableHeroLevel: t.TableHeroLevel.Clone().(*TableHeroLevelData),

		TableHook: t.TableHook.Clone().(*TableHookData),

		TableInviteTask: t.TableInviteTask.Clone().(*TableInviteTaskData),

		TableIronSourceADConfig: t.TableIronSourceADConfig.Clone().(*TableIronSourceADConfigData),

		TableItem: t.TableItem.Clone().(*TableItemData),

		TableLanguage: t.TableLanguage.Clone().(*TableLanguageData),

		TableMail: t.TableMail.Clone().(*TableMailData),

		TableMailTemplateConfig: t.TableMailTemplateConfig.Clone().(*TableMailTemplateConfigData),

		TableMainLine: t.TableMainLine.Clone().(*TableMainLineData),

		TableMainRank: t.TableMainRank.Clone().(*TableMainRankData),

		TableMainRankSeason: t.TableMainRankSeason.Clone().(*TableMainRankSeasonData),

		TableMidasDiamond: t.TableMidasDiamond.Clone().(*TableMidasDiamondData),

		TableMidasItem: t.TableMidasItem.Clone().(*TableMidasItemData),

		TableMission: t.TableMission.Clone().(*TableMissionData),

		TableModel: t.TableModel.Clone().(*TableModelData),

		TableMonsterBaseAttr: t.TableMonsterBaseAttr.Clone().(*TableMonsterBaseAttrData),

		TableMonthlyCard: t.TableMonthlyCard.Clone().(*TableMonthlyCardData),

		TableMonthlyCardNew: t.TableMonthlyCardNew.Clone().(*TableMonthlyCardNewData),

		TableMonthlyCardNewExtraReward: t.TableMonthlyCardNewExtraReward.Clone().(*TableMonthlyCardNewExtraRewardData),

		TableNewGuideLinesInfo: t.TableNewGuideLinesInfo.Clone().(*TableNewGuideLinesInfoData),

		TableNewGuideLinesTrigger: t.TableNewGuideLinesTrigger.Clone().(*TableNewGuideLinesTriggerData),

		TablePayment: t.TablePayment.Clone().(*TablePaymentData),

		TablePlayMode: t.TablePlayMode.Clone().(*TablePlayModeData),

		TablePlayerBaseAttr: t.TablePlayerBaseAttr.Clone().(*TablePlayerBaseAttrData),

		TablePlayerLevel: t.TablePlayerLevel.Clone().(*TablePlayerLevelData),

		TableQuestionnaire: t.TableQuestionnaire.Clone().(*TableQuestionnaireData),

		TableRandomName: t.TableRandomName.Clone().(*TableRandomNameData),

		TableRank: t.TableRank.Clone().(*TableRankData),

		TableRankBoard: t.TableRankBoard.Clone().(*TableRankBoardData),

		TableSchedulerConfig: t.TableSchedulerConfig.Clone().(*TableSchedulerConfigData),

		TableServerSkill: t.TableServerSkill.Clone().(*TableServerSkillData),

		TableSevenSignIn: t.TableSevenSignIn.Clone().(*TableSevenSignInData),

		TableShopGiftPack: t.TableShopGiftPack.Clone().(*TableShopGiftPackData),

		TableSignBonus: t.TableSignBonus.Clone().(*TableSignBonusData),

		TableSignIn: t.TableSignIn.Clone().(*TableSignInData),

		TableSynergy: t.TableSynergy.Clone().(*TableSynergyData),

		TableTeamHole: t.TableTeamHole.Clone().(*TableTeamHoleData),

		TableTimeGiftPacks: t.TableTimeGiftPacks.Clone().(*TableTimeGiftPacksData),

		TableTopupRebate: t.TableTopupRebate.Clone().(*TableTopupRebateData),

		TableTotalRecharge: t.TableTotalRecharge.Clone().(*TableTotalRechargeData),

		TableTowerReward: t.TableTowerReward.Clone().(*TableTowerRewardData),

		TableTreasure: t.TableTreasure.Clone().(*TableTreasureData),

		TableTreasureGacha: t.TableTreasureGacha.Clone().(*TableTreasureGachaData),

		TableTreasureGachaPro: t.TableTreasureGachaPro.Clone().(*TableTreasureGachaProData),

		TableTreasureLv: t.TableTreasureLv.Clone().(*TableTreasureLvData),

		TableTreasureStar: t.TableTreasureStar.Clone().(*TableTreasureStarData),

		TableaccruedRewards: t.TableaccruedRewards.Clone().(*TableaccruedRewardsData),
	}

	nt.list = []ITable{

		nt.TableActivity,

		nt.TableActivityBossDamage,

		nt.TableActivityCard,

		nt.TableActivityChallenge,

		nt.TableActivityDoorGift,

		nt.TableActivityExchangeShop,

		nt.TableActivityFund,

		nt.TableActivityGame,

		nt.TableActivityGift,

		nt.TableActivityHeroRoad,

		nt.TableActivityLimitGift,

		nt.TableActivityLotteryRewards,

		nt.TableActivityMiracleDrop,

		nt.TableActivitySevenDayTask,

		nt.TableActivityTask,

		nt.TableActivityTimeConfig,

		nt.TableActivityTurntable,

		nt.TableAttr,

		nt.TableAttrLevel,

		nt.TableAttrRange,

		nt.TableBuff,

		nt.TableChessHero,

		nt.TableCommonBoxReward,

		nt.TableDoorGift,

		nt.TableDropBox,

		nt.TableDropGroup,

		nt.TableEnergyBuy,

		nt.TableFCMTemplate,

		nt.TableFashionableDress,

		nt.TableFirstCharge,

		nt.TableFollowGift,

		nt.TableFunctionPreview,

		nt.TableFunctionUnlock,

		nt.TableGachaBonus,

		nt.TableGameConfig,

		nt.TableGiftCode,

		nt.TableGradedFund,

		nt.TableGradedFundInfo,

		nt.TableGuideLinesInfo,

		nt.TableGuideLinesTrigger,

		nt.TableGuildActivityBoss,

		nt.TableGuildConfig,

		nt.TableGuildDonate,

		nt.TableGuildLevel,

		nt.TableGuildShop,

		nt.TableGuildShopList,

		nt.TableGuildTech,

		nt.TableHeadFrame,

		nt.TableHeadIcon,

		nt.TableHeavenlyDao,

		nt.TableHero,

		nt.TableHeroEvo,

		nt.TableHeroLevel,

		nt.TableHook,

		nt.TableInviteTask,

		nt.TableIronSourceADConfig,

		nt.TableItem,

		nt.TableLanguage,

		nt.TableMail,

		nt.TableMailTemplateConfig,

		nt.TableMainLine,

		nt.TableMainRank,

		nt.TableMainRankSeason,

		nt.TableMidasDiamond,

		nt.TableMidasItem,

		nt.TableMission,

		nt.TableModel,

		nt.TableMonsterBaseAttr,

		nt.TableMonthlyCard,

		nt.TableMonthlyCardNew,

		nt.TableMonthlyCardNewExtraReward,

		nt.TableNewGuideLinesInfo,

		nt.TableNewGuideLinesTrigger,

		nt.TablePayment,

		nt.TablePlayMode,

		nt.TablePlayerBaseAttr,

		nt.TablePlayerLevel,

		nt.TableQuestionnaire,

		nt.TableRandomName,

		nt.TableRank,

		nt.TableRankBoard,

		nt.TableSchedulerConfig,

		nt.TableServerSkill,

		nt.TableSevenSignIn,

		nt.TableShopGiftPack,

		nt.TableSignBonus,

		nt.TableSignIn,

		nt.TableSynergy,

		nt.TableTeamHole,

		nt.TableTimeGiftPacks,

		nt.TableTopupRebate,

		nt.TableTotalRecharge,

		nt.TableTowerReward,

		nt.TableTreasure,

		nt.TableTreasureGacha,

		nt.TableTreasureGachaPro,

		nt.TableTreasureLv,

		nt.TableTreasureStar,

		nt.TableaccruedRewards,
	}
	return nt
}

func (t *TableLoader) GetTables() []ITable {
	return t.list
}
