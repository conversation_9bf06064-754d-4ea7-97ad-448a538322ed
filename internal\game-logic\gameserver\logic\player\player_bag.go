package player

import (
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/pkg/log"
	"math/rand"
)

// Bag 背包模块
type Bag struct {
	player *Player
	db     *dbstruct.Bag

	itemGuidMap map[int64]*public.PBNormalItemInfo   // guid -> item
	itemTypeMap map[int32][]*public.PBNormalItemInfo // itemId -> items
}

func NewBag(p *Player) *Bag {
	return &Bag{
		player:      p,
		itemGuidMap: make(map[int64]*public.PBNormalItemInfo),
		itemTypeMap: make(map[int32][]*public.PBNormalItemInfo),
	}
}

// InitDB 初始化模块数据
func (b *Bag) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Bag == nil {
		db.Game.Bag = &dbstruct.Bag{
			Items: make(map[int64]*public.PBNormalItemInfo),
		}
	}

	b.db = db.Game.Bag
	if b.db.Items == nil {
		log.Warn("Bag data structure not properly initialized InitDB", log.Kv("uid", b.player.Uid()))
		b.db.Items = make(map[int64]*public.PBNormalItemInfo)
	}

	b.itemGuidMap = make(map[int64]*public.PBNormalItemInfo)
	b.itemTypeMap = make(map[int32][]*public.PBNormalItemInfo)

	for guid, item := range b.db.Items {
		b.itemGuidMap[guid] = item
		b.itemTypeMap[item.ItemId] = append(b.itemTypeMap[item.ItemId], item)
	}

	log.Info("Bag InitDB")
}

// OnCrossDay 处理跨天逻辑
func (b *Bag) OnCrossDay(natural bool, nowUnix int64) {
	// 处理跨天逻辑，如清理过期道具等
	// TODO: 实现跨天逻辑
}

// SyncBagItemList 同步背包物品列表
// @Export
func (b *Bag) SyncBagItemList() {
	// 1. 构建同步消息
	resp := &cs.LCSyncBagItemListRst{
		BagItemList: b.buildPBBagItemList(),
	}

	// 2. 发送同步消息给客户端
	b.player.SendToClient(rpc_def.LCSyncBagItemListRst, resp, false)

	log.Info("SyncBagItemList")
}

// buildPBBagItemList 构建背包物品列表
func (b *Bag) buildPBBagItemList() []*public.PBBagItemInfo {
	bagItems := make([]*public.PBBagItemInfo, 0, len(b.itemGuidMap))

	for _, item := range b.itemGuidMap {
		bagItems = append(bagItems, &public.PBBagItemInfo{
			NormalItem: &public.PBNormalItemInfo{
				Guid:   item.Guid,
				ItemId: item.ItemId,
				Value:  item.Value,
			},
		})
		log.Info("buildPBBagItemList", log.Kv("Uid", b.player.Uid()), log.Kv("ItemId", item.ItemId), log.Kv("Value", item.Value))
	}

	return bagItems
}

// AddNormalItem 添加物品
// @Export
func (b *Bag) AddNormalItem(itemId int32, count int32, causeId int32) bool {
	reBool := b.AddNormalItemWithoutSync(itemId, count, causeId)
	b.SyncBagItemList()

	return reBool
}

// AddNormalItemWithoutSync 添加物品
// @Export
func (b *Bag) AddNormalItemWithoutSync(itemId int32, count int32, causeId int32) bool {
	// 检查背包空间
	if b.IsBagSpaceFull() {
		log.Warn("AddNormalItem lack of bag space")
		return false
	}

	// 1. 检查是否有相同ID的物品可以堆叠
	if items := b.itemTypeMap[itemId]; len(items) > 0 {
		// 找到第一个物品堆叠
		item := items[0]
		item.Value += count

		// 更新数据库
		if dbItem, ok := b.db.Items[item.Guid]; ok {
			dbItem.Value = item.Value
		}

		log.Info("AddNormalItem stack success",
			log.Kv("itemId", itemId),
			log.Kv("count", count),
			log.Kv("causeId", causeId),
			log.Kv("totalValue", item.Value))
		return true
	}

	// 2. 没有可堆叠的物品，创建新物品
	guid := rand.Int63() // TODO 等道具系统提供接口后替换

	item := public.PBNormalItemInfo{
		Guid:   guid,
		ItemId: itemId,
		Value:  count,
	}

	if b.db.Items == nil {
		log.Warn("Bag data structure not properly initialized", log.Kv("uid", b.player.Uid()))
		b.db.Items = make(map[int64]*public.PBNormalItemInfo)
	}

	// 更新数据库
	b.db.Items[guid] = &item

	// 更新缓存
	b.itemGuidMap[guid] = &item
	b.itemTypeMap[itemId] = append(b.itemTypeMap[itemId], &item)

	log.Info("AddNormalItem new success",
		log.Kv("itemId", itemId),
		log.Kv("count", count),
		log.Kv("causeId", causeId),
		log.Kv("guid", guid))
	return true
}

// DeductNormalItem 扣除物品
// @Export
func (b *Bag) DeductNormalItem(itemId int32, count int32, operateReason int32) bool {
	items := b.itemTypeMap[itemId]
	if items == nil || len(items) == 0 {
		log.Warn("DeductNormalItem no item found",
			log.Kv("itemId", itemId))
		return false
	}

	// 检查总数是否足够
	totalCount := int32(0)
	for _, item := range items {
		totalCount += item.Value
	}

	// 如果count为0，表示删除所有
	if count == 0 {
		count = totalCount
	}

	if totalCount < count {
		log.Warn("DeductNormalItem not enough items",
			log.Kv("itemId", itemId),
			log.Kv("required", count),
			log.Kv("current", totalCount))
		return false
	}

	remainCount := count
	newItems := make([]*public.PBNormalItemInfo, 0, len(items))

	for _, item := range items {
		if remainCount <= 0 {
			newItems = append(newItems, item)
			continue
		}

		if item.Value <= remainCount {
			// 整个堆都被消耗
			remainCount -= item.Value
			delete(b.itemGuidMap, item.Guid)
			delete(b.db.Items, item.Guid)
		} else {
			// 部分消耗
			item.Value -= remainCount
			if dbItem, ok := b.db.Items[item.Guid]; ok {
				dbItem.Value = item.Value
			}
			remainCount = 0
			newItems = append(newItems, item)
		}
	}

	// 更新物品类型映射
	if len(newItems) == 0 {
		delete(b.itemTypeMap, itemId)
	} else {
		b.itemTypeMap[itemId] = newItems
	}

	// 同步到客户端
	b.SyncBagItemList()

	log.Info("DeductNormalItem success",
		log.Kv("itemId", itemId),
		log.Kv("count", count),
		log.Kv("remainingStacks", len(newItems)))
	return true
}

// ClearItemsByID 清理指定ID的物品
// @Export
func (b *Bag) ClearItemsByID(itemId int32) {
	items := b.itemTypeMap[itemId]
	if items == nil {
		return
	}

	// 从所有map中删除
	for _, item := range items {
		delete(b.itemGuidMap, item.Guid)
		delete(b.db.Items, item.Guid)
	}
	delete(b.itemTypeMap, itemId)

	// 同步到客户端
	b.SyncBagItemList()

	log.Info("ClearItemsByID success",
		log.Kv("itemId", itemId),
		log.Kv("clearedCount", len(items)))
}

// clearAllNormalItems 清理所有普通物品
func (b *Bag) clearAllNormalItems() {
	b.itemGuidMap = make(map[int64]*public.PBNormalItemInfo)
	b.itemTypeMap = make(map[int32][]*public.PBNormalItemInfo)
	b.db.Items = make(map[int64]*public.PBNormalItemInfo)

	// 同步到客户端
	b.SyncBagItemList()
}

// CheckNormalItemCount 检查物品数量是否足够
// @Export
func (b *Bag) CheckNormalItemCount(itemId int32, count int32) bool {
	items := b.itemTypeMap[itemId]
	totalCount := int32(0)
	for _, item := range items {
		totalCount += item.Value
	}
	return totalCount >= count
}

// GetNormalItemCount 获取物品数量
// @Export
func (b *Bag) GetNormalItemCount(itemId int32) int32 {
	items := b.itemTypeMap[itemId]
	totalCount := int32(0)
	for _, item := range items {
		totalCount += item.Value
	}
	return totalCount
}

// removeFromTypeMap 从类型映射中移除指定guid的物品
func (b *Bag) removeFromTypeMap(itemId int32, guid int64) {
	items := b.itemTypeMap[itemId]
	for i, item := range items {
		if item.Guid == guid {
			b.itemTypeMap[itemId] = append(items[:i], items[i+1:]...)
			break
		}
	}
	// 如果该类型没有物品了，删除该类型的映射
	if len(b.itemTypeMap[itemId]) == 0 {
		delete(b.itemTypeMap, itemId)
	}
}

// IsBagSpaceFull 背包空间是否已满
func (b *Bag) IsBagSpaceFull() bool {
	return b.getBagOccupyNum() >= game_def.PlayerBagSize
}

// GetBagOccupyNum 获取背包已被占用的格子数
func (b *Bag) getBagOccupyNum() int {
	return len(b.itemGuidMap)
}
