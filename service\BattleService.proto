syntax = "proto3";

import "descriptor.proto";
import "PublicMessage.proto";
import "PublicEnum.proto";

package natsrpc;
option go_package = "liteframe/internal/common/natsrpc";
option csharp_namespace = "BattleServer.Service";


// 创建战斗信息
message PBCreateBattleInfo
{
  repeated PBBattlePlayerInfo players = 1; // 玩家信息
  repeated PBBattleTeamInfo teams = 2;     // 阵容信息
}

message CreateBattleReq {
  PBCreateBattleInfo createInfo = 1;
}

message CreateBattleResp {
  int32 code = 1;
  int64 serverId = 2;
  int64 battleId = 3;
}

message EnterBattleReq {
  uint64 uid = 1; // 玩家ID
}

message EnterBattleResp {
  int32 code = 1;
}

message SelectBufferReq {
  uint64 uid = 1; // 玩家ID
  int32 bufferID = 2;
}

message SelectBufferResp {
  int32 code = 1;
  repeated PBCheckerBoard newHeroes = 2; // 选择Buff后，本回合新生成的英雄信息
}

message MergeHeroReq
{
  uint64 uid = 1; 	// 玩家ID
  int32 from = 2;	// 合成源格子ID
  int32 to = 3; 	// 合成目标格子ID
  repeated PBMoveOperation moves = 4; // 本次合成前发生的所有移动操作
}

message MergeHeroResp
{
  int32 code = 1;
  int32 from = 2;	//合成源格子ID
  int32 to = 3; //合成目标格子ID
  repeated PBCheckerBoard newHeros = 4; // 合成的新英雄数据
}

//准备
message ReadyBattleReq
{
	uint64 uid = 1; // 玩家ID
	repeated PBMoveOperation moves = 2; // 确认准备前要同步的移动操作
}

//准备
message ReadyBattleResp
{
	int32 code = 1;
}

message EndBattleReq {
  uint64 uid = 1; // 玩家ID
  bool win = 2;
}

message EndBattleResp {
  int32 code = 1;
}

// 离开战斗请求
message LeaveBattleReq {
    uint64 uid = 1;  // 玩家ID
}

// 离开战斗响应
message LeaveBattleResp {
    int32 code = 1;  // 响应码：0=成功，其他=失败
}

service BattleService {

  rpc CreateBattle(CreateBattleReq) returns (CreateBattleResp);
  rpc EnterBattle(EnterBattleReq) returns (EnterBattleResp) {
    option (use_server_id) = true;
  };
  rpc SelectBuffer(SelectBufferReq) returns (SelectBufferResp) {
    option (use_server_id) = true;
  };
  rpc MergeHero(MergeHeroReq) returns (MergeHeroResp) {
    option (use_server_id) = true;
  };

  rpc BattleReady(ReadyBattleReq) returns (ReadyBattleResp){
      option (use_server_id) = true;
  };
  rpc EndBattle(EndBattleReq) returns (EndBattleResp){
      option (use_server_id) = true;
  };
  rpc LeaveBattle(LeaveBattleReq) returns (LeaveBattleResp) {
      option (use_server_id) = true;
  }
}