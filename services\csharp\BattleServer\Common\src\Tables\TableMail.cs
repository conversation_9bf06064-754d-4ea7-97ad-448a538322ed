#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMail
	{

		public static readonly string TName="Mail.json";

		#region 属性定义
		/// <summary> 
		/// 邮件唯一id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 有效时间 
		/// </summary> 
		public int duration {get; set;}
		/// <summary> 
		/// 邮件附件 
		/// </summary> 
		public string reward {get; set;}
		/// <summary> 
		/// 邮件标题 
		/// </summary> 
		public int title {get; set;}
		/// <summary> 
		/// 邮件内容 
		/// </summary> 
		public int content {get; set;}
		/// <summary> 
		/// 发件人名称 
		/// </summary> 
		public int from {get; set;}
		#endregion

		public static TableMail GetData(int ID)
		{
			return TableManager.MailData.Get(ID);
		}

		public static List<TableMail> GetAllData()
		{
			return TableManager.MailData.GetAll();
		}

	}
	public sealed partial class TableMailData
	{
		private Dictionary<int, TableMail> dict = new Dictionary<int, TableMail>();
		private List<TableMail> dataList = new List<TableMail>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMail.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMail>>(jsonContent);
			foreach (TableMail config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMail Get(int id)
		{
			if (dict.TryGetValue(id, out TableMail item))
				return item;
			return null;
		}

		public List<TableMail> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
